# Задача 009: Функциональность редактирования заметок

## Описание задачи
Добавление функциональности редактирования заметок в приложение messenger-notes. Пользователи должны иметь возможность изменять содержимое существующих заметок с сохранением изменений и обновлением временной метки.

## Цели
- Реализовать возможность редактирования содержимого существующих заметок
- Добавить UI элементы для инициации и управления процессом редактирования
- Обеспечить валидацию данных и обработку ошибок при редактировании
- Интегрировать функциональность с существующей архитектурой приложения
- Обновлять временную метку последнего изменения при сохранении

## Чеклист задач
- [x] Добавить поле `updated_at` в схему базы данных для таблицы `notes`
- [x] Создать API endpoint `PUT /api/notes/[id]` для обновления заметок
- [x] Обновить типы данных в `types/notes.ts` для поддержки редактирования
- [x] Добавить функцию `updateNote` в хук `useNotes.ts`
- [x] Создать компонент `EditNoteDialog` для формы редактирования
- [x] Добавить кнопку "Редактировать" в компонент `NotesList`
- [x] Интегрировать функциональность редактирования в главную страницу заметок
- [x] Реализовать валидацию данных при редактировании
- [x] Добавить обработку ошибок и состояний загрузки
- [ ] Протестировать полный цикл редактирования заметок

## Технические детали

### Изменения в базе данных:
- Добавить поле `updated_at TIMESTAMP WITH TIME ZONE` в таблицу `notes`
- Создать миграцию для добавления нового поля

### API endpoints:
- `PUT /api/notes/[id]` - обновление существующей заметки
  - Проверка авторизации пользователя
  - Валидация входных данных
  - Проверка принадлежности заметки пользователю
  - Обновление содержимого и временной метки

### Компоненты UI:
- `EditNoteDialog` - модальное окно для редактирования заметки
  - Форма с предзаполненными данными
  - Поддержка Markdown режима
  - Кнопки "Сохранить" и "Отмена"
  - Обработка состояний загрузки

### Интеграция:
- Добавить кнопку редактирования в `NotesList`
- Обновить состояние заметок после успешного редактирования
- Сохранить существующие паттерны UX и дизайна

## Архитектурные принципы
- Следовать существующим паттернам кода в проекте
- Использовать те же компоненты UI (shadcn/ui)
- Поддерживать консистентность с существующими API endpoints
- Обеспечить типобезопасность TypeScript
- Реализовать proper error handling

## Прогресс
- [x] Файл задачи создан
- [x] Анализ существующей архитектуры завершен
- [x] Миграция базы данных создана
- [x] API endpoint реализован
- [x] Типы данных обновлены
- [x] Хук useNotes расширен
- [x] Компонент EditNoteDialog создан
- [x] Интеграция в NotesList выполнена
- [ ] Тестирование завершено

## Результаты
(Будет заполнено по мере выполнения задачи)
