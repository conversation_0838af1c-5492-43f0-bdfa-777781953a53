# Задача 010: Реализация ИИ-функциональности

## Описание задачи
Реализовать функциональность искусственного интеллекта в рамках текущего функционала мессенджер-заметок. Интегрировать ИИ для автоматической обработки заметок (генерация тегов и саммари) и реализовать семантический поиск.

## Анализ текущего состояния

### Существующая архитектура:
- **Frontend**: Next.js с React, Tailwind CSS, shadcn/ui компоненты
- **Backend**: Next.js API Routes
- **База данных**: Supabase PostgreSQL с таблицами:
  - `notes` (id, user_id, content_type, content, summary_ai, created_at)
  - `tags` (id, name)
  - `note_tags` (note_id, tag_id)
- **Аутентификация**: Supabase Auth
- **Текущая система тегов**: Простая генерация из первых слов заметки

### Существующие компоненты:
- `SearchAndFilter` - поиск и фильтрация по тегам
- `NoteComposer` - создание заметок с Markdown поддержкой
- `NotesList` - отображение заметок
- API routes: `/api/notes`, `/api/tags`, `/api/notes/[id]`
- Hooks: `useNotes`, `useTags`

### Подготовленные места для ИИ:
- Поле `summary_ai` в таблице `notes` (пока null)
- Поиск уже учитывает `summary_ai` в фильтрации
- Архитектура готова для расширения

## Требования из PRD

### Ключевые ИИ-функции:
1. **Автоматическая обработка заметок**: Каждая новая заметка анализируется мультимодальной LLM для генерации тегов и краткого описания
2. **Семантический поиск**: Поиск на естественном языке с использованием ИИ для анализа запроса и поиска релевантных заметок
3. **Гибридный поиск**: Сочетание поиска по тегам и семантического поиска

### Технические требования:
- Использовать **Vercel AI SDK** для интеграции с LLM
- Мультимодальная модель (например, GPT-4o) для обработки текста, PDF и изображений
- Сохранение тегов и саммари в существующую БД структуру

## План реализации

### Этап 1: Установка и настройка Vercel AI SDK
- [x] Установить `ai` и `@openrouter/ai-sdk-provider` пакеты (для работы с OpenRouter)
- [x] Настроить переменные окружения для OpenRouter API
- [x] Создать утилиты для работы с ИИ

### Этап 2: Реализация ИИ-обработки заметок
- [x] Создать API endpoint `/api/ai/analyze-note` для анализа заметок
- [x] Реализовать функцию генерации тегов и саммари через ИИ
- [x] Интегрировать ИИ-обработку в процесс создания заметок
- [x] Обновить существующую логику сохранения тегов

### Этап 3: Реализация семантического поиска
- [x] Создать API endpoint `/api/ai/search` для семантического поиска
- [x] Реализовать логику анализа поискового запроса через ИИ
- [x] Обновить компонент `SearchAndFilter` для поддержки ИИ-поиска
- [x] Добавить индикацию режима поиска (по тегам vs семантический)

### Этап 4: Улучшение UX
- [x] Добавить индикаторы загрузки для ИИ-операций
- [x] Реализовать обработку ошибок ИИ-сервисов
- [x] Добавить skeleton loader для тегов во время ИИ-анализа
- [x] Реализовать real-time обновление заметок после ИИ-анализа
- [x] Убрать старую систему автоматических тегов
- [ ] Добавить возможность повторной обработки заметок
- [ ] Оптимизировать производительность

### Этап 5: Тестирование и отладка
- [ ] Протестировать генерацию тегов и саммари
- [ ] Протестировать семантический поиск
- [ ] Проверить совместимость с существующим функционалом
- [ ] Оптимизировать промпты для лучших результатов

## Технические детали

### Структура новых API endpoints:
- `POST /api/ai/analyze-note` - анализ заметки для генерации тегов и саммари
- `POST /api/ai/search` - семантический поиск заметок

### Новые утилиты:
- `lib/ai/openrouter.ts` - конфигурация OpenRouter клиента
- `lib/ai/prompts.ts` - промпты для ИИ
- `lib/ai/analyze.ts` - функции анализа заметок
- `lib/ai/search.ts` - функции семантического поиска

### Обновления существующих компонентов:
- `SearchAndFilter` - добавить режим семантического поиска
- `app/api/notes/route.ts` - интегрировать ИИ-обработку
- `hooks/useNotes.ts` - добавить поддержку ИИ-операций

### Переменные окружения:
```
OPENROUTER_API_KEY=your_openrouter_api_key
```

## Риски и ограничения

### Технические риски:
- Зависимость от внешнего API (OpenRouter)
- Возможные задержки в обработке заметок
- Стоимость использования ИИ-сервисов

### Минимизация рисков:
- Асинхронная обработка заметок (не блокирует создание)
- Обработка ошибок и fallback на простые теги
- Оптимизация промптов для снижения затрат
- Кэширование результатов где возможно
- Гибкость выбора модели через OpenRouter

## Критерии успеха
- [ ] Новые заметки автоматически получают ИИ-сгенерированные теги и саммари
- [ ] Семантический поиск находит релевантные заметки по естественным запросам
- [ ] Существующий функционал продолжает работать без изменений
- [ ] UX остается интуитивным и быстрым
- [ ] Система устойчива к ошибкам ИИ-сервисов

## Реализованные файлы

### Новые файлы:
- `lib/ai/openrouter.ts` - конфигурация OpenRouter клиента
- `lib/ai/prompts.ts` - промпты для ИИ-анализа и поиска
- `lib/ai/analyze.ts` - функции анализа заметок с fallback
- `lib/ai/search.ts` - функции семантического поиска
- `app/api/ai/analyze-note/route.ts` - API endpoint для анализа заметок
- `app/api/ai/search/route.ts` - API endpoint для семантического поиска
- `app/api/notes/[id]/refresh/route.ts` - API endpoint для получения обновленной заметки
- `components/notes/TagsSkeleton.tsx` - skeleton loader для тегов во время ИИ-анализа
- `.env.example` - пример переменных окружения

### Обновленные файлы:
- `app/api/notes/route.ts` - убрана старая система тегов, добавлена интеграция с ИИ-анализом
- `components/notes/SearchAndFilter.tsx` - добавлена поддержка ИИ-поиска
- `components/notes/NotesList.tsx` - добавлена поддержка skeleton loader для тегов
- `app/notes/page.tsx` - добавлена логика ИИ-поиска и индикаторы
- `hooks/useNotes.ts` - добавлен polling для real-time обновлений
- `types/notes.ts` - добавлено поле `isAnalyzing` для состояния ИИ-анализа
- `package.json` - добавлены зависимости `ai` и `@ai-sdk/openai`

## Статус: Реализовано (требует тестирования)

## Прогресс
- [x] Анализ существующей кодовой базы
- [x] Изучение требований из PRD
- [x] Составление детального плана реализации
- [x] Установка и настройка Vercel AI SDK с OpenRouter
- [x] Создание базовых утилит для работы с ИИ
- [x] Реализация API endpoints для ИИ-анализа и поиска
- [x] Интеграция ИИ-обработки в создание заметок
- [x] Обновление UI для поддержки семантического поиска
- [x] Добавление индикаторов загрузки и результатов ИИ-поиска
- [x] Удаление старой системы автоматических тегов
- [x] Реализация real-time обновления тегов через polling
- [x] Добавление skeleton loader для тегов во время ИИ-анализа
- [x] Улучшение UX с плавными переходами состояний
- [ ] Тестирование функциональности
- [ ] Оптимизация промптов и производительности

## Инструкция по настройке

### 1. Настройка переменных окружения
Добавьте в файл `.env.local`:
```
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 2. Получение API ключа OpenRouter
1. Зарегистрируйтесь на https://openrouter.ai/
2. Создайте API ключ в настройках аккаунта
3. Добавьте ключ в переменные окружения

### 3. Использование ИИ-функций

#### Автоматическая обработка заметок:
- При создании новой текстовой заметки автоматически запускается ИИ-анализ
- ИИ генерирует релевантные теги и краткое описание
- Обработка происходит асинхронно, не блокируя создание заметки
- При ошибках ИИ используются простые теги из первых слов

#### Семантический поиск:
- Введите поисковый запрос в строку поиска
- Нажмите Enter или кнопку с иконкой звездочки для ИИ-поиска
- ИИ найдет релевантные заметки на основе смысла запроса
- Результаты отображаются с индикатором "ИИ-поиск"
- Можно комбинировать с фильтрацией по тегам

### 4. Модели по умолчанию
- Основная модель: `openai/gpt-4o-mini` (быстрая и экономичная)
- Мультимодальная модель: `openai/gpt-4o` (для будущих функций с файлами)

### 5. Fallback механизмы
- При недоступности ИИ используются простые теги
- При ошибках поиска возвращается пустой результат
- Все ошибки логируются в консоль

## Реализованные улучшения UX

### 1. Удаление старой системы тегов
- Полностью убрана генерация простых тегов при создании заметок
- Fallback на простые теги остался только в функции `analyzeNote()` при ошибках ИИ
- Новые заметки создаются без тегов и ждут ИИ-анализа

### 2. Real-time обновление тегов
- Реализован polling механизм для автоматического обновления заметок
- Заметки обновляются каждые 2 секунды в течение 24 секунд после создания
- Обновление происходит автоматически без необходимости обновления страницы
- После получения тегов от ИИ polling останавливается

### 3. Skeleton loader для тегов
- Использован shadcn/ui Skeleton компонент для консистентного дизайна
- Показывается индикатор "ИИ генерирует теги..." с вращающейся иконкой
- Skeleton имеет разную ширину для имитации реальных тегов
- Плавная замена skeleton на реальные теги после завершения анализа

### 4. Улучшенная индикация состояний
- Новые заметки помечаются флагом `isAnalyzing: true`
- Визуальная обратная связь о процессе генерации тегов
- Плавные переходы между состояниями загрузки и готовности
- Автоматическое обновление списка тегов для поиска

### 5. Исправлено поведение поиска
- Enter в поле поиска всегда запускает ИИ-поиск (убрано условие отсутствия предложений тегов)
- Добавлен preventDefault для корректной обработки Enter
- Улучшена логика взаимодействия с предложениями тегов
