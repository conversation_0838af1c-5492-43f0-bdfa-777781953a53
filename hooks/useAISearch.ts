import { useState } from 'react'
import { Note } from '@/types/notes'

interface AISearchState {
  isSearching: boolean
  results: Note[] | null
  error: string | null
  query: string
}

interface AISearchActions {
  search: (query: string) => Promise<void>
  clearResults: () => void
  clearError: () => void
  reset: () => void
}

const initialState: AISearchState = {
  isSearching: false,
  results: null,
  error: null,
  query: ''
}

export function useAISearch(): AISearchState & AISearchActions {
  const [state, setState] = useState<AISearchState>(initialState)

  const search = async (query: string) => {
    if (!query.trim()) return

    setState(prev => ({
      ...prev,
      isSearching: true,
      error: null,
      query: query.trim()
    }))

    try {
      const response = await fetch('/api/ai/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: query.trim() })
      })

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          isSearching: false,
          results: data.notes,
          error: null
        }))
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Неизвестная ошибка' }))
        setState(prev => ({
          ...prev,
          isSearching: false,
          error: errorData.error || 'Ошибка при выполнении ИИ-поиска'
        }))
        console.error('AI search failed:', response.status, errorData)
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isSearching: false,
        error: 'Не удалось выполнить ИИ-поиск. Проверьте подключение к интернету.'
      }))
      console.error('AI search error:', error)
    }
  }

  const clearResults = () => {
    setState(prev => ({
      ...prev,
      results: null,
      query: ''
    }))
  }

  const clearError = () => {
    setState(prev => ({
      ...prev,
      error: null
    }))
  }

  const reset = () => {
    setState(initialState)
  }

  return {
    ...state,
    search,
    clearResults,
    clearError,
    reset
  }
}
