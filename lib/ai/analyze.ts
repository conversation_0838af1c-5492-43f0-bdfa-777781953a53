import { generateObject } from 'ai'
import { getModel, DEFAULT_MODEL } from './openrouter'
import { ANALYZE_NOTE_PROMPT } from './prompts'
import { generateSimpleTags } from '@/lib/utils/tags'
import { replacePlaceholders, truncateContent, AI_CONSTANTS } from './utils'
import { aiLogger } from './logger'
import { analyzeResultSchema, type AnalyzeResult } from './schemas'

export async function analyzeNote(content: string): Promise<AnalyzeResult> {
  const logContext = {
    operation: 'analyze' as const,
    model: DEFAULT_MODEL,
    temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE
  }

  try {
    const prompt = replacePlaceholders(ANALYZE_NOTE_PROMPT, {
      noteContent: content
    })

    aiLogger.logRequest(logContext, prompt)

    const { object } = await generateObject({
      model: getModel(DEFAULT_MODEL),
      prompt,
      temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE,
      schema: analyzeResultSchema,
    })

    aiLogger.logResponse(logContext, object)

    const finalResult = {
      tags: object.tags.slice(0, AI_CONSTANTS.MAX_TAGS_COUNT),
      summary: object.summary
    }

    aiLogger.logResult(logContext, finalResult)

    return finalResult
  } catch (error) {
    aiLogger.logError(logContext, error)

    const fallbackResult = {
      tags: generateSimpleTags(content),
      summary: truncateContent(content, AI_CONSTANTS.SUMMARY_FALLBACK_LENGTH)
    }

    aiLogger.logFallback(logContext, fallbackResult, 'AI analysis failed')

    return fallbackResult
  }
}
