import { createOpenRouter } from '@openrouter/ai-sdk-provider';

if (!process.env.OPENROUTER_API_KEY) {
  throw new Error('OPENROUTER_API_KEY environment variable is required but not set')
}

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

export const DEFAULT_MODEL = 'google/gemini-2.5-flash-lite-preview-06-17'
export const MULTIMODAL_MODEL = 'google/gemini-2.5-flash-lite-preview-06-17'

export function getModel(modelName: string = DEFAULT_MODEL) {
  return openrouter(modelName)
}
