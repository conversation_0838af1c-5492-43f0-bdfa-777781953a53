import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export interface AuthenticatedRequest {
  user: {
    id: string
    email?: string
  }
  supabase: Awaited<ReturnType<typeof createClient>>
}

export async function withAuth(
  request: NextRequest,
  handler: (req: NextRequest, auth: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    return await handler(request, {
      user: { id: user.id, email: user.email },
      supabase: await supabase
    })
  } catch (error) {
    console.error('Authentication middleware error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export function validateRequestBody<T>(
  body: any,
  validator: (body: any) => { isValid: boolean; error?: string; data?: T }
): { isValid: boolean; error?: string; data?: T } {
  if (!body || typeof body !== 'object') {
    return { isValid: false, error: 'Invalid request body' }
  }

  return validator(body)
}

export function createErrorResponse(message: string, status: number = 500): NextResponse {
  return NextResponse.json({ error: message }, { status })
}

export function createSuccessResponse<T>(data: T): NextResponse {
  return NextResponse.json(data)
}

export function validateString(value: any, fieldName: string, required: boolean = true): string | null {
  if (!value) {
    return required ? `${fieldName} is required` : null
  }

  if (typeof value !== 'string') {
    return `${fieldName} must be a string`
  }

  if (required && !value.trim()) {
    return `${fieldName} cannot be empty`
  }

  return null
}
