import { createClient } from '@/lib/supabase/server'
import { Note } from '@/types/notes'
import { DatabaseNote, DatabaseError } from './types'
import { analyzeNote } from '@/lib/ai/analyze'
import { addTagsToNote } from './tags'

export async function getAllNotesByUserId(userId: string): Promise<Note[]> {
  try {
    const supabase = await createClient()

    const { data: notes, error } = await supabase
      .from('notes')
      .select(`
        id,
        content,
        content_type,
        summary_ai,
        created_at,
        updated_at,
        note_tags (
          tags (
            id,
            name
          )
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error fetching notes:', error)
      throw new DatabaseError('Could not fetch notes from the database', error)
    }

    return formatNotesFromDatabase(notes || [])
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error fetching notes:', error)
    throw new DatabaseError('Unexpected error while fetching notes', error)
  }
}

export async function getNoteByIdAndUserId(noteId: string, userId: string): Promise<Note> {
  try {
    const supabase = await createClient()

    const { data: note, error } = await supabase
      .from('notes')
      .select(`
        id,
        content,
        content_type,
        summary_ai,
        created_at,
        updated_at,
        note_tags (
          tags (
            id,
            name
          )
        )
      `)
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (error || !note) {
      throw new DatabaseError('Note not found or access denied')
    }

    return formatNoteFromDatabase(note)
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error fetching note:', error)
    throw new DatabaseError('Unexpected error while fetching note', error)
  }
}

export async function createNote(
  userId: string,
  content: string,
  contentType: 'text' | 'file' | 'link' = 'text'
): Promise<Note> {
  try {
    const supabase = await createClient()

    const { data: note, error } = await supabase
      .from('notes')
      .insert({
        user_id: userId,
        content: content.trim(),
        content_type: contentType,
        summary_ai: null
      })
      .select()
      .single()

    if (error) {
      console.error('Database error creating note:', error)
      throw new DatabaseError('Could not create note in the database', error)
    }

    return formatNoteFromDatabase(note, [])
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error creating note:', error)
    throw new DatabaseError('Unexpected error while creating note', error)
  }
}

export async function updateNote(
  noteId: string,
  userId: string,
  content: string
): Promise<Note> {
  try {
    const supabase = await createClient()

    const { data: existingNote, error: checkError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (checkError || !existingNote) {
      throw new DatabaseError('Note not found or access denied')
    }

    const { data: updatedNote, error: updateError } = await supabase
      .from('notes')
      .update({
        content: content.trim()
      })
      .eq('id', noteId)
      .eq('user_id', userId)
      .select(`
        id,
        content,
        content_type,
        summary_ai,
        created_at,
        updated_at,
        note_tags (
          tags (
            id,
            name
          )
        )
      `)
      .single()

    if (updateError) {
      console.error('Database error updating note:', updateError)
      throw new DatabaseError('Could not update note in the database', updateError)
    }

    return formatNoteFromDatabase(updatedNote)
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error updating note:', error)
    throw new DatabaseError('Unexpected error while updating note', error)
  }
}

export async function deleteNote(noteId: string, userId: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { data: existingNote, error: checkError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (checkError || !existingNote) {
      throw new DatabaseError('Note not found or access denied')
    }

    const { error: deleteError } = await supabase
      .from('notes')
      .delete()
      .eq('id', noteId)
      .eq('user_id', userId)

    if (deleteError) {
      console.error('Database error deleting note:', deleteError)
      throw new DatabaseError('Could not delete note from the database', deleteError)
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error deleting note:', error)
    throw new DatabaseError('Unexpected error while deleting note', error)
  }
}

export async function updateNoteSummary(noteId: string, summary: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('notes')
      .update({ summary_ai: summary })
      .eq('id', noteId)

    if (error) {
      console.error('Database error updating note summary:', error)
      throw new DatabaseError('Could not update note summary in the database', error)
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error updating note summary:', error)
    throw new DatabaseError('Unexpected error while updating note summary', error)
  }
}

function formatNotesFromDatabase(notes: DatabaseNote[]): Note[] {
  return notes.map(note => formatNoteFromDatabase(note))
}

export async function analyzeNoteInBackground(noteId: string, content: string): Promise<boolean> {
  try {
    const analysis = await analyzeNote(content)

    await updateNoteSummary(noteId, analysis.summary)
    await addTagsToNote(noteId, analysis.tags)

    return true
  } catch (error) {
    console.error('Background AI analysis failed:', error)
    return false
  }
}

export async function updateNoteWithTags(
  noteId: string,
  userId: string,
  summary: string,
  tags: string[]
): Promise<{ success: boolean; tags: string[]; summary: string }> {
  try {
    const supabase = await createClient()

    const { data: note, error: checkError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (checkError || !note) {
      throw new DatabaseError('Note not found or access denied')
    }

    const { data: result, error: rpcError } = await supabase
      .rpc('update_note_with_tags', {
        p_note_id: noteId,
        p_user_id: userId,
        p_summary: summary,
        p_tags: tags
      })

    if (rpcError) {
      console.error('Database error updating note with tags:', rpcError)
      throw new DatabaseError('Could not update note with tags in the database', rpcError)
    }

    if (!result || !result.success) {
      throw new DatabaseError('Failed to update note with tags')
    }

    return {
      success: true,
      tags: result.tags || tags,
      summary: result.summary || summary
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error updating note with tags:', error)
    throw new DatabaseError('Unexpected error while updating note with tags', error)
  }
}

function formatNoteFromDatabase(note: DatabaseNote, tags?: string[]): Note {
  return {
    id: note.id,
    content: note.content,
    content_type: note.content_type,
    summary_ai: note.summary_ai,
    created_at: note.created_at,
    updated_at: note.updated_at || note.created_at,
    tags: tags || note.note_tags?.map((nt: any) => nt.tags.name) || []
  }
}
