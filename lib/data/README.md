# Data Access Layer (DAL)

Этот модуль предоставляет единую точку доступа к данным в приложении. Все взаимодействие с базой данных Supabase должно происходить через функции этого модуля.

## Принципы

1. **Единая точка истины** - вся логика работы с БД находится здесь
2. **Изоляция** - API роуты не должны напрямую обращаться к Supabase
3. **Типизация** - все функции строго типизированы
4. **Обработка ошибок** - централизованная обработка через `DatabaseError`

## Структура

```
lib/data/
├── types.ts          # Типы для работы с БД и класс DatabaseError
├── notes.ts          # Операции с заметками
├── tags.ts           # Операции с тегами
└── README.md         # Этот файл
```

## Использование

### Импорт функций

```typescript
import { getAllNotesByUserId, createNote } from '@/lib/data/notes'
import { getAllTagsByUserId } from '@/lib/data/tags'
import { DatabaseError } from '@/lib/data/types'
```

### Обработка ошибок

Все функции DAL могут выбрасывать `DatabaseError`. В API роутах обрабатывайте их так:

```typescript
try {
  const notes = await getAllNotesByUserId(user.id)
  return NextResponse.json({ notes })
} catch (error) {
  if (error instanceof DatabaseError) {
    return NextResponse.json({ error: 'Failed to fetch notes' }, { status: 500 })
  }
  return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
}
```

## API функций

### Notes

#### `getAllNotesByUserId(userId: string): Promise<Note[]>`
Получает все заметки пользователя, отсортированные по дате создания (новые первыми).

#### `getNoteByIdAndUserId(noteId: string, userId: string): Promise<Note>`
Получает одну заметку по ID, проверяя принадлежность пользователю.

#### `createNote(userId: string, content: string, contentType?: string): Promise<Note>`
Создает новую заметку. По умолчанию `contentType = 'text'`.

#### `updateNote(noteId: string, userId: string, content: string): Promise<Note>`
Обновляет содержимое заметки с проверкой прав доступа.

#### `deleteNote(noteId: string, userId: string): Promise<void>`
Удаляет заметку с проверкой прав доступа.

#### `updateNoteSummary(noteId: string, summary: string): Promise<void>`
Обновляет AI-резюме заметки.

#### `analyzeNoteInBackground(noteId: string, content: string): Promise<boolean>`
Запускает фоновый AI-анализ заметки (обновляет резюме и теги).

#### `updateNoteWithTags(noteId: string, userId: string, summary: string, tags: string[]): Promise<{...}>`
Обновляет заметку с резюме и тегами через RPC функцию БД.

### Tags

#### `getAllTagsByUserId(userId: string): Promise<string[]>`
Получает все уникальные теги пользователя.

#### `createOrGetTag(tagName: string): Promise<string>`
Создает новый тег или возвращает ID существующего.

#### `addTagsToNote(noteId: string, tags: string[]): Promise<void>`
Добавляет теги к заметке (создает теги если их нет).

## Расширение модуля

При добавлении новых функций:

1. Добавьте функцию в соответствующий файл (`notes.ts` или `tags.ts`)
2. Экспортируйте её из файла с помощью `export`
3. Используйте `DatabaseError` для обработки ошибок
4. Добавьте типизацию для всех параметров и возвращаемых значений
5. Обновите эту документацию

## Замена БД

Если в будущем потребуется заменить Supabase на другую БД:

1. Измените только реализацию функций в этом модуле
2. API роуты останутся без изменений
3. Интерфейсы функций должны остаться теми же
4. Обновите только импорт клиента БД в начале файлов
