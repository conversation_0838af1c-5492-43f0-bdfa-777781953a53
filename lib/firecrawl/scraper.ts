import { firecrawl, isFirecrawlEnabled } from './client'

export interface ScrapeResult {
  success: boolean
  content?: string
  title?: string
  url?: string
  error?: string
}

const SCRAPE_TIMEOUT = 30000 // 30 seconds
const MAX_CONTENT_LENGTH = 50000 // 50KB limit for content

export async function scrapeUrl(url: string): Promise<ScrapeResult> {
  if (!isFirecrawlEnabled || !firecrawl) {
    return {
      success: false,
      error: 'Firecrawl is not configured'
    }
  }

  try {
    console.log(`[Firecrawl] Starting scrape for URL: ${url}`)

    const startTime = Date.now()

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Scrape timeout')), SCRAPE_TIMEOUT)
    })

    // Race between scrape and timeout
    const scrapePromise = firecrawl.scrapeUrl(url, {
      formats: ['markdown'],
      onlyMainContent: true,
      includeTags: ['title', 'meta'],
      excludeTags: ['nav', 'footer', 'aside', 'script', 'style'],
      timeout: SCRAPE_TIMEOUT
    })

    const result = await Promise.race([scrapePromise, timeoutPromise])

    const duration = Date.now() - startTime
    console.log(`[Firecrawl] Scrape completed in ${duration}ms`)

    if (!result.success) {
      console.error(`[Firecrawl] Scrape failed:`, result.error)
      return {
        success: false,
        error: result.error || 'Unknown scrape error'
      }
    }

    console.log(`[Firecrawl] Raw result structure:`, JSON.stringify(result, null, 2))
    console.log(`[Firecrawl] result.data:`, result.data)
    console.log(`[Firecrawl] result.data?.markdown length:`, result.data?.markdown?.length || 0)
    console.log(`[Firecrawl] result.data?.content length:`, result.data?.content?.length || 0)

    const content = result.data?.markdown || result.data?.content || ''
    const title = result.data?.metadata?.title || ''

    // Limit content length to prevent excessive AI processing costs
    const truncatedContent = content.length > MAX_CONTENT_LENGTH
      ? content.substring(0, MAX_CONTENT_LENGTH) + '\n\n[Content truncated...]'
      : content

    console.log(`[Firecrawl] Successfully scraped ${truncatedContent.length} characters`)

    return {
      success: true,
      content: truncatedContent,
      title: title,
      url: url
    }

  } catch (error) {
    const duration = Date.now() - Date.now()
    console.error(`[Firecrawl] Scrape error after ${duration}ms:`, error)

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during scraping'
    }
  }
}

export async function scrapeMultipleUrls(urls: string[]): Promise<ScrapeResult[]> {
  if (!isFirecrawlEnabled) {
    return urls.map(url => ({
      success: false,
      url,
      error: 'Firecrawl is not configured'
    }))
  }

  console.log(`[Firecrawl] Starting batch scrape for ${urls.length} URLs`)

  // Process URLs in parallel but with a reasonable limit
  const MAX_CONCURRENT = 3
  const results: ScrapeResult[] = []

  for (let i = 0; i < urls.length; i += MAX_CONCURRENT) {
    const batch = urls.slice(i, i + MAX_CONCURRENT)
    const batchPromises = batch.map(url => scrapeUrl(url))
    const batchResults = await Promise.allSettled(batchPromises)

    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      } else {
        results.push({
          success: false,
          url: batch[index],
          error: result.reason?.message || 'Failed to scrape URL'
        })
      }
    })
  }

  console.log(`[Firecrawl] Batch scrape completed: ${results.filter(r => r.success).length}/${results.length} successful`)

  return results
}
