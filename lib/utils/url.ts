// URL patterns for different formats
const URL_PATTERNS = [
  // Standard HTTP/HTTPS URLs - improved to handle dashes and more characters
  /https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.-])*(?:\?(?:[\w&=%.-])*)?(?:\#(?:[\w.-])*)?)?/gi,
  // URLs starting with www - improved to handle dashes and more characters
  /www\.(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.-])*(?:\?(?:[\w&=%.-])*)?(?:\#(?:[\w.-])*)?)?/gi,
  // Domain-only URLs (basic pattern)
  /(?:^|\s)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/\S*)?(?=\s|$)/gi
]

export function extractUrls(text: string): string[] {
  const urls = new Set<string>()

  // Apply each pattern to find URLs
  URL_PATTERNS.forEach(pattern => {
    const matches = text.match(pattern)
    if (matches) {
      matches.forEach(match => {
        const cleanUrl = match.trim()
        if (isValidUrl(cleanUrl)) {
          urls.add(normalizeUrl(cleanUrl))
        }
      })
    }
  })

  return Array.from(urls)
}

export function isValidUrl(url: string): boolean {
  try {
    // Add protocol if missing
    const normalizedUrl = normalizeUrl(url)
    const urlObj = new URL(normalizedUrl)

    // Check for valid protocol
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false
    }

    // Check for valid hostname
    if (!urlObj.hostname || urlObj.hostname.length < 3) {
      return false
    }

    // Check for at least one dot in hostname (basic domain validation)
    if (!urlObj.hostname.includes('.')) {
      return false
    }

    // Exclude localhost and IP addresses for scraping
    if (urlObj.hostname === 'localhost' ||
        urlObj.hostname.startsWith('127.') ||
        urlObj.hostname.startsWith('192.168.') ||
        urlObj.hostname.startsWith('10.') ||
        /^\d+\.\d+\.\d+\.\d+$/.test(urlObj.hostname)) {
      return false
    }

    return true
  } catch {
    return false
  }
}

export function normalizeUrl(url: string): string {
  let normalized = url.trim()

  // Add protocol if missing
  if (!normalized.startsWith('http://') && !normalized.startsWith('https://')) {
    // Prefer https for www. URLs and common domains
    if (normalized.startsWith('www.') ||
        normalized.includes('github.com') ||
        normalized.includes('google.com') ||
        normalized.includes('stackoverflow.com')) {
      normalized = 'https://' + normalized
    } else {
      normalized = 'http://' + normalized
    }
  }

  try {
    const urlObj = new URL(normalized)
    // Remove trailing slash for consistency
    if (urlObj.pathname === '/') {
      urlObj.pathname = ''
    }
    return urlObj.toString()
  } catch {
    return normalized
  }
}

export function containsUrl(text: string): boolean {
  return extractUrls(text).length > 0
}

export function getFirstUrl(text: string): string | null {
  const urls = extractUrls(text)
  return urls.length > 0 ? urls[0] : null
}

export function replaceUrlsWithPlaceholders(text: string): { text: string; urls: string[] } {
  const urls = extractUrls(text)
  let processedText = text

  urls.forEach((url, index) => {
    const placeholder = `[URL_${index + 1}]`
    processedText = processedText.replace(url, placeholder)
  })

  return { text: processedText, urls }
}

// Utility to check if a note content is primarily a URL
export function isPrimaryUrl(content: string): boolean {
  const trimmed = content.trim()
  const urls = extractUrls(trimmed)

  if (urls.length === 0) return false

  // If there's only one URL and it takes up most of the content
  if (urls.length === 1) {
    const url = urls[0]
    const contentWithoutUrl = trimmed.replace(url, '').trim()
    // Consider it a primary URL if the remaining content is very short
    return contentWithoutUrl.length < 50
  }

  return false
}

// Get domain from URL for display purposes
export function getDomainFromUrl(url: string): string {
  try {
    const urlObj = new URL(normalizeUrl(url))
    return urlObj.hostname.replace(/^www\./, '')
  } catch {
    return url
  }
}
