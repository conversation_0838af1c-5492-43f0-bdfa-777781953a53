import { useState } from "react"
import { Send, Edit3, <PERSON><PERSON><PERSON>, ImageI<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

interface NoteComposerProps {
  onSubmit: (content: string) => Promise<void>
  userEmail?: string
  isSubmitting?: boolean
}

export function NoteComposer({ onSubmit, userEmail, isSubmitting = false }: NoteComposerProps) {
  const [content, setContent] = useState("")
  const [isMarkdownMode, setIsMarkdownMode] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!content.trim() || submitting || isSubmitting) return

    setSubmitting(true)
    try {
      await onSubmit(content.trim())
      setContent("")
      setIsMarkdownMode(false)
    } finally {
      setSubmitting(false)
    }
  }

  const isDisabled = !content.trim() || submitting || isSubmitting

  return (
    <Card className="py-1">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">Новая заметка</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMarkdownMode(!isMarkdownMode)}
              className={isMarkdownMode ? "bg-muted" : ""}
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Markdown
            </Button>
          </div>

          <Textarea
            placeholder={isMarkdownMode ? "Введите текст с поддержкой Markdown..." : "Введите вашу заметку..."}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="min-h-[100px] resize-none"
          />

          {isMarkdownMode && content && (
            <div className="border rounded-md p-3 bg-muted/50">
              <div className="text-xs text-muted-foreground mb-2">Предварительный просмотр:</div>
              <div className="prose max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" disabled>
                <Paperclip className="h-4 w-4 mr-1" />
                Файл
              </Button>
              <Button variant="ghost" size="sm" disabled>
                <ImageIcon className="h-4 w-4 mr-1" />
                Изображение
              </Button>
            </div>
            <Button onClick={handleSubmit} disabled={isDisabled}>
              <Send className="h-4 w-4 mr-1" />
              {(submitting || isSubmitting) ? 'Сохранение...' : 'Отправить'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
