import { useState } from "react"
import { <PERSON>, Hash, X, <PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"

interface SearchAndFilterProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  onAISearch: (query: string) => void
  selectedTags: string[]
  onTagAdd: (tag: string) => void
  onTagRemove: (tag: string) => void
  availableTags: string[]
  isAISearching?: boolean
}

export function SearchAndFilter({
  searchQuery,
  onSearchChange,
  onAISearch,
  selectedTags,
  onTagAdd,
  onTagRemove,
  availableTags,
  isAISearching = false
}: SearchAndFilterProps) {
  const [showTagSuggestions, setShowTagSuggestions] = useState(false)

  const suggestedTags = availableTags
    .filter((tag) =>
      tag.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !selectedTags.includes(tag)
    )
    .slice(0, 5)

  const handleSearchChange = (query: string) => {
    onSearchChange(query)
    setShowTagSuggestions(query.length > 0)
  }

  const handleTagAdd = (tag: string) => {
    onTagAdd(tag)
    onSearchChange("")
    setShowTagSuggestions(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && searchQuery.trim()) {
      e.preventDefault()
      onAISearch(searchQuery.trim())
      setShowTagSuggestions(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* Поиск */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Поиск заметок, введите тег или нажмите Enter для ИИ-поиска..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="pl-10 pr-12"
            onFocus={() => setShowTagSuggestions(searchQuery.length > 0)}
            disabled={isAISearching}
          />
          {searchQuery.trim() && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              onClick={() => onAISearch(searchQuery.trim())}
              disabled={isAISearching}
            >
              <Sparkles className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Предложения тегов */}
        {showTagSuggestions && suggestedTags.length > 0 && (
          <div className="absolute top-full mt-1 w-full z-10 bg-card text-card-foreground rounded-xl border shadow-sm py-2">
            <div className="px-2">
              <div className="space-y-1">
                {suggestedTags.map((tag) => (
                  <Button
                    key={tag}
                    variant="ghost"
                    className="w-full justify-start h-8 px-3"
                    onClick={() => handleTagAdd(tag)}
                  >
                    <Hash className="h-3 w-3 mr-2" />
                    {tag}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Выбранные теги */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map((tag) => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              <Hash className="h-3 w-3" />
              {tag}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onTagRemove(tag)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
