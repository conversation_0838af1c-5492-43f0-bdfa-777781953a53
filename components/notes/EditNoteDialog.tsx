import { useState, useEffect } from "react"
import { Edit3, Save, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import { Note } from "@/types/notes"

interface EditNoteDialogProps {
  note: Note | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (noteId: string, content: string) => Promise<void>
  userEmail?: string
  isSaving?: boolean
}

export function EditNoteDialog({
  note,
  open,
  onOpenChange,
  onSave,
  userEmail,
  isSaving = false
}: EditNoteDialogProps) {
  const [content, setContent] = useState("")
  const [isMarkdownMode, setIsMarkdownMode] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Обновляем содержимое при изменении заметки
  useEffect(() => {
    if (note) {
      setContent(note.content)
      setHasChanges(false)
    }
  }, [note])

  // Отслеживаем изменения в содержимом
  useEffect(() => {
    if (note) {
      setHasChanges(content !== note.content)
    }
  }, [content, note])

  const handleSave = async () => {
    if (!note || !content.trim() || !hasChanges || isSaving) return

    try {
      await onSave(note.id, content.trim())
      onOpenChange(false)
    } catch (error) {
      // Ошибка будет обработана в родительском компоненте
    }
  }

  const handleCancel = () => {
    if (note) {
      setContent(note.content)
      setHasChanges(false)
    }
    setIsMarkdownMode(false)
    onOpenChange(false)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ru-RU", {
      day: "numeric",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (!note) return null

  const isDisabled = !content.trim() || !hasChanges || isSaving

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Edit3 className="h-5 w-5" />
            <span>Редактировать заметку</span>
          </DialogTitle>
          <DialogDescription>
            Внесите изменения в содержимое заметки и нажмите "Сохранить"
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Информация о заметке */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">Вы</div>
                <div className="text-xs text-muted-foreground">
                  Создано: {formatDate(note.created_at)}
                  {note.updated_at !== note.created_at && (
                    <span> • Изменено: {formatDate(note.updated_at)}</span>
                  )}
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMarkdownMode(!isMarkdownMode)}
              className={isMarkdownMode ? "bg-muted" : ""}
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Markdown
            </Button>
          </div>

          {/* Поле редактирования */}
          <div className="flex-1 overflow-hidden flex flex-col space-y-3">
            <Textarea
              placeholder={isMarkdownMode ? "Введите текст с поддержкой Markdown..." : "Введите содержимое заметки..."}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="flex-1 min-h-[200px] resize-none"
            />

            {/* Предварительный просмотр Markdown */}
            {isMarkdownMode && content && (
              <div className="flex-1 border rounded-md p-3 bg-muted/50 overflow-auto">
                <div className="text-xs text-muted-foreground mb-2">Предварительный просмотр:</div>
                <div className="prose max-w-none">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {content}
                  </ReactMarkdown>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {hasChanges && "У вас есть несохраненные изменения"}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
              <X className="h-4 w-4 mr-1" />
              Отмена
            </Button>
            <Button onClick={handleSave} disabled={isDisabled}>
              <Save className="h-4 w-4 mr-1" />
              {isSaving ? 'Сохранение...' : 'Сохранить'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
