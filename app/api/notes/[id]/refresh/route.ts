import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { getNoteByIdAndUserId } from '@/lib/data/notes'
import { DatabaseError } from '@/lib/data/types'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: noteId } = await params

    if (!noteId) {
      return NextResponse.json({ error: 'Note ID is required' }, { status: 400 })
    }

    const note = await getNoteByIdAndUserId(noteId, user.id)
    return NextResponse.json({ note })
  } catch (error) {
    console.error('Error in GET /api/notes/[id]/refresh:', error)

    if (error instanceof DatabaseError) {
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return NextResponse.json({ error: 'Note not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to fetch note' }, { status: 500 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
