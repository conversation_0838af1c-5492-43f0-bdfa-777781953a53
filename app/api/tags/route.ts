import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { TagsResponse } from '@/types/notes'
import { getAllTagsByUserId } from '@/lib/data/tags'
import { DatabaseError } from '@/lib/data/types'

export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const tags = await getAllTagsByUserId(user.id)
    return NextResponse.json({ tags } as TagsResponse)
  } catch (error) {
    console.error('Error in GET /api/tags:', error)

    if (error instanceof DatabaseError) {
      return NextResponse.json({ error: 'Failed to fetch tags' }, { status: 500 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
