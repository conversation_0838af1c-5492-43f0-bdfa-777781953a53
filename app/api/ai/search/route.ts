import { NextRequest } from 'next/server'
import { searchNotesWithAI } from '@/lib/ai/search'
import { Note } from '@/types/notes'
import { withAuth, validateRequestBody, validateString, createErrorResponse, createSuccessResponse } from '@/lib/api/middleware'
import { getAllNotesByUserId } from '@/lib/data/notes'
import { DatabaseError } from '@/lib/data/types'

interface SearchRequest {
  query: string
}

function validateSearchRequest(body: any): { isValid: boolean; error?: string; data?: SearchRequest } {
  const queryError = validateString(body.query, 'Search query')
  if (queryError) return { isValid: false, error: queryError }

  return {
    isValid: true,
    data: { query: body.query.trim() }
  }
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (req, { user }) => {
    try {
      const body = await req.json()
      const validation = validateRequestBody(body, validateSearchRequest)

      if (!validation.isValid) {
        return createErrorResponse(validation.error!, 400)
      }

      const { query } = validation.data!

      const notes = await getAllNotesByUserId(user.id)
      const foundNoteIds = await searchNotesWithAI(query, notes)

      const foundNotes = foundNoteIds
        .map(id => notes.find(note => note.id === id))
        .filter(Boolean) as Note[]

      return createSuccessResponse({
        notes: foundNotes,
        query: query
      })
    } catch (error) {
      console.error('Error in AI search:', error)

      if (error instanceof DatabaseError) {
        return createErrorResponse('Failed to fetch notes')
      }

      return createErrorResponse('Internal server error')
    }
  })
}
