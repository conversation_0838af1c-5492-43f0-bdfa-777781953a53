// Debug script to test URL detection
const testUrl = 'https://www.donnamagi.com/articles/karpathy-yc-talk'

console.log('Testing URL:', testUrl)

// Test URL patterns manually (updated)
const URL_PATTERNS = [
  /https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.-])*(?:\?(?:[\w&=%.-])*)?(?:\#(?:[\w.-])*)?)?/gi,
  /www\.(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.-])*(?:\?(?:[\w&=%.-])*)?(?:\#(?:[\w.-])*)?)?/gi,
  /(?:^|\s)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/\S*)?(?=\s|$)/gi
]

console.log('\nTesting patterns:')
URL_PATTERNS.forEach((pattern, index) => {
  const matches = testUrl.match(pattern)
  console.log(`Pattern ${index + 1}:`, matches)
})

// Test URL validation
try {
  const urlObj = new URL(testUrl)
  console.log('\nURL object:', {
    protocol: urlObj.protocol,
    hostname: urlObj.hostname,
    pathname: urlObj.pathname
  })
} catch (e) {
  console.log('URL parsing failed:', e.message)
}

console.log('\nDone!')
