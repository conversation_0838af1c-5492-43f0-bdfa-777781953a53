-- Создание функции для атомарного обновления заметки с тегами
-- Решает проблемы N+1 запросов и отсутствия атомарности

CREATE OR REPLACE FUNCTION update_note_with_tags(
  p_note_id UUID,
  p_user_id UUID,
  p_summary TEXT,
  p_tags TEXT[]
) RETURNS JSON AS $$
DECLARE
  tag_record RECORD;
  tag_id UUID;
  existing_tag_ids UUID[];
  new_tag_names TEXT[];
  result JSON;
BEGIN
  -- Проверяем, что заметка принадлежит пользователю
  IF NOT EXISTS (
    SELECT 1 FROM notes 
    WHERE id = p_note_id AND user_id = p_user_id
  ) THEN
    RAISE EXCEPTION 'Note not found or access denied';
  END IF;

  -- Обновляем summary заметки
  UPDATE notes 
  SET summary_ai = p_summary, updated_at = NOW()
  WHERE id = p_note_id AND user_id = p_user_id;

  -- Удаляем все существующие связи тегов с заметкой
  DELETE FROM note_tags WHERE note_id = p_note_id;

  -- Если нет тегов для добавления, возвращаем результат
  IF array_length(p_tags, 1) IS NULL OR array_length(p_tags, 1) = 0 THEN
    SELECT json_build_object(
      'success', true,
      'tags', '[]'::json,
      'summary', p_summary
    ) INTO result;
    RETURN result;
  END IF;

  -- Находим существующие теги одним запросом
  SELECT array_agg(id), array_agg(name)
  INTO existing_tag_ids, new_tag_names
  FROM tags 
  WHERE name = ANY(p_tags);

  -- Определяем, какие теги нужно создать
  SELECT array_agg(tag_name)
  INTO new_tag_names
  FROM unnest(p_tags) AS tag_name
  WHERE tag_name NOT IN (
    SELECT name FROM tags WHERE name = ANY(p_tags)
  );

  -- Создаем новые теги одним запросом, если они есть
  IF array_length(new_tag_names, 1) > 0 THEN
    INSERT INTO tags (name)
    SELECT unnest(new_tag_names)
    ON CONFLICT (name) DO NOTHING;
  END IF;

  -- Получаем все ID тегов (существующих и новых) одним запросом
  SELECT array_agg(id)
  INTO existing_tag_ids
  FROM tags 
  WHERE name = ANY(p_tags);

  -- Создаем связи между заметкой и тегами одним запросом
  INSERT INTO note_tags (note_id, tag_id)
  SELECT p_note_id, unnest(existing_tag_ids)
  ON CONFLICT (note_id, tag_id) DO NOTHING;

  -- Формируем результат
  SELECT json_build_object(
    'success', true,
    'tags', json_agg(name ORDER BY name),
    'summary', p_summary
  )
  INTO result
  FROM tags 
  WHERE id = ANY(existing_tag_ids);

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    -- В случае ошибки откатываем транзакцию и возвращаем ошибку
    RAISE EXCEPTION 'Failed to update note with tags: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Даем права на выполнение функции аутентифицированным пользователям
GRANT EXECUTE ON FUNCTION update_note_with_tags(UUID, UUID, TEXT, TEXT[]) TO authenticated;
