-- Add updated_at column to notes table
ALTER TABLE notes ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE;

-- Set initial value for existing records to match created_at
UPDATE notes SET updated_at = created_at WHERE updated_at IS NULL;

-- Make updated_at NOT NULL after setting initial values
ALTER TABLE notes ALTER COLUMN updated_at SET NOT NULL;

-- Set default value for new records
ALTER TABLE notes ALTER COLUMN updated_at SET DEFAULT TIMEZONE('utc', NOW());

-- Create index for better performance on updated_at queries
CREATE INDEX idx_notes_updated_at ON notes(updated_at DESC);

-- Create or replace function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc', NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on UPDATE
CREATE TRIGGER update_notes_updated_at 
    BEFORE UPDATE ON notes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
